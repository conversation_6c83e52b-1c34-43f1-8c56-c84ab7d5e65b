/* Custom CSS for Navbar and Website */

/* Navbar Styles */
.navbar {
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    display: flex;
    align-items: center;
}

.navbar-logo {
    height: 40px;
    width: auto;
    transition: transform 0.3s ease;
}

.navbar-logo:hover {
    transform: scale(1.05);
}

/* Navigation Links */
.navbar-nav .nav-link {
    font-weight: 500;
    color: #333 !important;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: #f8f9fa;
    color: #007bff !important;
}

.navbar-nav .nav-link.active {
    color: #007bff !important;
    background-color: #e3f2fd;
}

/* Call-to-action button */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* Navbar shadow on scroll */
.navbar.scrolled {
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* Mobile navbar styles */
@media (max-width: 991.98px) {
    .navbar-nav {
        text-align: center;
        margin-top: 1rem;
    }
    
    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }
    
    .d-flex.ms-3 {
        justify-content: center;
        margin-top: 1rem;
        margin-left: 0 !important;
    }
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-section h1 {
    color: white;
}

.hero-section .lead {
    color: rgba(255, 255, 255, 0.9);
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Section padding */
section {
    padding: 4rem 0;
}

/* Responsive image */
.img-fluid {
    max-width: 100%;
    height: auto;
}

/* Custom button styles */
.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    border-radius: 0.5rem;
}

.btn-outline-secondary {
    color: white;
    border-color: white;
}

.btn-outline-secondary:hover {
    background-color: white;
    border-color: white;
    color: #333;
}

/* Navbar toggler custom styles */
.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Logo container */
#navbar-logo {
    text-decoration: none;
}

/* Ensure navbar stays on top */
.navbar {
    z-index: 1030;
}

/* Additional spacing for sections */
.py-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
}

/* Background colors */
.bg-light {
    background-color: #f8f9fa !important;
}

/* Text center utility */
.text-center {
    text-align: center !important;
}

/* Margin bottom for headings */
.mb-5 {
    margin-bottom: 3rem !important;
}

/* Container max width */
.container {
    max-width: 1200px;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 0;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .navbar-logo {
        height: 35px;
    }
}

/* Animation for smooth transitions */
* {
    transition: all 0.3s ease;
}

/* Focus states for accessibility */
.nav-link:focus,
.btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}
